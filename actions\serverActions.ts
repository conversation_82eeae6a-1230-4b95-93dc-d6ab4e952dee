'use server'
import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'
import * as supabaseQueries from '@/utils/supabase/queries'
import * as elevenlabsQueries from '@/utils/elevenlabs/queries'
import { tryCatch } from '@/utils/try-catch'
import {z} from 'zod';
import * as elevenLabsSync from '@/utils/sync/elevenlabs-sync';


export async function fetchAgents() {
  const supabase = await createClient()
  const { data, error } = await supabase
    .from('agents')
    .select('*')

  if (error) {
    throw new Error(error.message)
  }

  return data
}



type State = {
  success: boolean
  id?: string
  error?: string
}

const AGENT_TEMPLATES = supabaseQueries.getTemplates();


export async function createAgent(prevState: State, formData: FormData): Promise<State> {
  const agentSchema = z.object(
    {
      name: z.string().min(1, { message: 'Name is required' }),
      template: z.string().min(1, { message: 'Template is required' }),
      orgId: z.string().min(1, { message: 'Organization ID is required' }),
      teamId: z.string().optional(),
      budget: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
    }
  )
  const formValues = {
    name: formData.get('name')?.toString() ?? '',
    template: formData.get('template')?.toString() ?? '',
    orgId: formData.get('orgId')?.toString() ?? '',
    teamId: formData.get('teamId')?.toString() ?? undefined,
    budget: formData.get('budget')?.toString() ?? undefined,
  };



    const [error, agent] = await tryCatch(()=>agentSchema.parse(formValues))
    if (error) {
      return { success: false, error: error.message }
    }

    const name = agent.name;
    const template = agent.template
    const orgId = agent.orgId
    // If teamId is 'none', set it to undefined so it will be treated as null in the database
    const teamId = agent.teamId === 'none' ? undefined : agent.teamId
    const conversation_config = AGENT_TEMPLATES.find(t => t.id === template)?.conversation_config

    try {
      const supabase = await createClient();

      // First create the ElevenLabs agent
      const formattedName = `${name} [SB-${orgId}]`;
      const [elevenlabsError, elevenLabsAgent] = await tryCatch(() =>
        elevenlabsQueries.createAgent(formattedName, conversation_config)
      );

      if (elevenlabsError) {
        console.error('Error creating ElevenLabs agent:', elevenlabsError);
        return { success: false, error: elevenlabsError.message };
      }

      // Then create the agent in Supabase with the ElevenLabs agent ID
      const [supabaseError, supabaseAgent] = await tryCatch(() =>
        supabaseQueries.createAgent(
          supabase,
          name,
          orgId,
          conversation_config, // Pass the original conversation_config
          teamId,
          agent.budget,
          elevenLabsAgent.agent_id // Pass the ElevenLabs agent ID
        )
      );

      if (supabaseError) {
        // If Supabase creation fails, delete the ElevenLabs agent to avoid orphaned resources
        try {
          await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
        } catch (deleteError) {
          console.error('Error deleting orphaned ElevenLabs agent:', deleteError);
        }

        return { success: false, error: supabaseError.message };
      }

      return { success: true, id: supabaseAgent.id };
    } catch (error) {
      console.error('Error in createAgent:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }

}
export async function updateAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;
  const teamId = formData.get('teamId') as string;
  const budget = formData.get('budget') as string;
  const widgetSettings = formData.get('widgetSettings') as string;

  // Voice settings
  const voice = formData.get('voice') as string;
  const voiceLanguage = formData.get('voiceLanguage') as string;
  const latencyOptimization = formData.get('latencyOptimization') as string;
  const stability = formData.get('stability') as string;
  const speed = formData.get('speed') as string;
  const similarity = formData.get('similarity') as string;

  // Conversation settings (from general tab)
  const firstMessage = formData.get('firstMessage') as string;
  const prompt = formData.get('prompt') as string;
  const language = formData.get('language') as string;
  const additionalLanguages = formData.get('additionalLanguages') as string;
  const llmProvider = formData.get('llmProvider') as string;
  const temperature = formData.get('temperature') as string;
  const tokenLimit = formData.get('tokenLimit') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Prepare update data for agent table
    const updateData: any = {};

    // Handle team assignment
    if (teamId) {
      // If teamId is 'none', set to null, otherwise use the provided ID
      updateData.team_id = teamId === 'none' ? null : teamId;
    }

    // Handle budget update
    if (budget) {
      // Convert dollars to cents
      updateData.budget_cents = Math.round(parseFloat(budget) * 100);
    }

    // Update agent table if needed
    if (Object.keys(updateData).length > 0) {
      const { data, error } = await supabase
        .from('agents')
        .update(updateData)
        .eq('id', agentId)
        .select()
        .single();

      if (error) {
        console.error('Error updating agent:', error);
        return { success: false, error: error.message };
      }
    }

    // Handle widget settings update if provided
    if (widgetSettings) {
      try {
        const widgetSettingsObj = JSON.parse(widgetSettings);

        // Get the current agent
        const { data: agent, error: agentError } = await supabase
          .from('agents')
          .select('config')
          .eq('id', agentId)
          .single();

        if (agentError) {
          console.error('Error fetching agent:', agentError);
          return { success: false, error: agentError.message };
        }

        // Merge the existing config with the new widget settings in the ui key
        const currentConfig = (agent?.config as Record<string, any>) || {};
        const updatedConfig = {
          ...currentConfig,
          ui: {
            ...(currentConfig.ui || {}),
            ...widgetSettingsObj
          }
        };

        const { error: updateError } = await supabase
          .from('agents')
          .update({ config: updatedConfig })
          .eq('id', agentId);

        if (updateError) {
          console.error('Error updating agent config:', updateError);
          return { success: false, error: updateError.message };
        }
      } catch (parseError) {
        console.error('Error parsing widget settings:', parseError);
        return {
          success: false,
          error: parseError instanceof Error ? parseError.message : 'Invalid widget settings format'
        };
      }
    }

    // Handle voice settings update if any voice-related fields are provided
    const hasVoiceSettings = voice || voiceLanguage || latencyOptimization || stability ||
                            speed || similarity || firstMessage || prompt || language ||
                            llmProvider || temperature || tokenLimit || additionalLanguages;

    if (hasVoiceSettings) {
      try {
        // Get the current agent
        const { data: agent, error: agentError } = await supabase
          .from('agents')
          .select('*')
          .eq('id', agentId)
          .single();

        if (agentError || !agent) {
          console.error('Error fetching agent:', agentError);
          return { success: false, error: 'Agent not found' };
        }

        // Get current config
        const currentConfig = (agent.config as any) || {};

        // Parse additional languages if provided
        let parsedAdditionalLanguages = [];
        if (additionalLanguages) {
          try {
            parsedAdditionalLanguages = JSON.parse(additionalLanguages);
          } catch (error) {
            console.error('Error parsing additional languages:', error);
          }
        }

        // Update config with voice settings and conversation settings
        const updatedConfig = {
          ...currentConfig,
          // Update language if provided
          ...(language && { language: language }),
          // Update additional languages if provided
          ...(parsedAdditionalLanguages.length > 0 && { additional_languages: parsedAdditionalLanguages }),
          // Update first message if provided
          ...(firstMessage && { first_message: firstMessage }),
          // Update prompt config if any prompt-related fields are provided
          prompt_config: {
            ...currentConfig.prompt_config,
            ...(prompt && { prompt: prompt }),
            ...(llmProvider && { llm: llmProvider }),
            ...(temperature && { temperature: parseFloat(temperature) }),
            ...(tokenLimit && { max_tokens: parseInt(tokenLimit) })
          },
          // Update TTS config if any voice-related fields are provided
          tts_config: {
            ...currentConfig.tts_config,
            ...(voice && { voice_id: voice }),
            model_id: currentConfig.tts_config?.model_id || 'eleven_turbo_v2',
            ...(voiceLanguage && { voice_language: voiceLanguage }),
            agent_output_audio_format: currentConfig.tts_config?.agent_output_audio_format || 'pcm_16000',
            ...(latencyOptimization && { optimize_streaming_latency: parseFloat(latencyOptimization) }),
            ...(stability && { stability: parseFloat(stability) }),
            ...(similarity && { similarity_boost: parseFloat(similarity) })
          }
        };

        // Update agent config
        await supabase
          .from('agents')
          .update({ config: updatedConfig })
          .eq('id', agentId);

        // Sync with ElevenLabs if there's an external agent ID and agent type is elevenlabs
        if (agent.external_id && agent.type === 'elevenlabs') {
          try {
            const agentName = agent.name || `Agent ${agentId}`;
            const formattedName = `${agentName} [SB-${orgId}]`;

            // Update the ElevenLabs agent with the new conversation config
            const conversationConfig = {
              agent: {
                language: updatedConfig.language || 'en',
                first_message: updatedConfig.first_message || '',
                prompt: {
                  prompt: updatedConfig.prompt_config?.prompt || '',
                  llm: updatedConfig.prompt_config?.llm || 'gpt-4o-mini',
                  temperature: updatedConfig.prompt_config?.temperature || 0.7,
                  max_tokens: updatedConfig.prompt_config?.max_tokens || -1,
                  tools: updatedConfig.prompt_config?.tools || [],
                  knowledge_base: updatedConfig.prompt_config?.knowledge_base || []
                }
              },
              tts: {
                model_id: updatedConfig.tts_config?.model_id || 'eleven_turbo_v2',
                voice_id: updatedConfig.tts_config?.voice_id || 'cjVigY5qzO86Huf0OWal',
                agent_output_audio_format: updatedConfig.tts_config?.agent_output_audio_format || 'pcm_16000',
                optimize_streaming_latency: updatedConfig.tts_config?.optimize_streaming_latency || 0,
                stability: updatedConfig.tts_config?.stability || 0.5,
                similarity_boost: updatedConfig.tts_config?.similarity_boost || 0.8,
                speed: 1
              },
              conversation: {
                max_duration_seconds: 600,
                client_events: ["conversation_initiation_metadata"]
              }
            };

            console.log('Syncing with ElevenLabs:', JSON.stringify(conversationConfig, null, 2));

            await elevenlabsQueries.updateAgent(
              agent.external_id,
              formattedName,
              conversationConfig
            );
          } catch (syncError) {
            console.error('Error syncing with ElevenLabs:', syncError);
            // Don't fail the operation if ElevenLabs sync fails
          }
        }
      } catch (voiceError) {
        console.error('Error updating voice settings:', voiceError);
        return {
          success: false,
          error: voiceError instanceof Error ? voiceError.message : 'Failed to update voice settings'
        };
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);
    revalidatePath(`/dashboard/${orgId}/agents/${agentId}`);

    return { success: true, id: agentId };
  } catch (error) {
    console.error('Error in updateAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
/**
 * Delete an agent and its associated resources
 */
export async function deleteAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Get agent details to check for ElevenLabs agent ID
    const { data: agent } = await supabase
      .from('agents')
      .select('external_id, type')
      .eq('id', agentId)
      .single();

    // Check if there's an ElevenLabs agent ID
    let elevenLabsAgentId: string | undefined;
    if (agent?.external_id && agent.type === 'elevenlabs') {
      elevenLabsAgentId = agent.external_id;
    }

    // Delete the agent from Supabase
    const [deleteError, success] = await tryCatch(() =>
      supabaseQueries.deleteAgent(supabase, agentId)
    );

    if (deleteError) {
      console.error('Error deleting agent:', deleteError);
      return { success: false, error: deleteError.message };
    }

    // If there's an ElevenLabs agent ID, delete it from ElevenLabs too
    if (elevenLabsAgentId) {
      const syncResult = await elevenLabsSync.deleteElevenLabsAgent(elevenLabsAgentId);

      if (!syncResult.elevenLabsSuccess) {
        console.error('Error deleting ElevenLabs agent:', syncResult.elevenLabsError);
        // Log the error but don't fail the operation since the Supabase deletion was successful
        // Consider adding this to a "sync repair queue" for later reconciliation
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);

    return { success: true };
  } catch (error) {
    console.error('Error in deleteAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function duplicateAgent(prevState: State, formData: FormData): Promise<State> {
  const agentId = formData.get('agentId') as string;
  const orgId = formData.get('orgId') as string;
  const newName = formData.get('newName') as string;

  if (!agentId || !orgId) {
    console.error('Missing agentId or orgId in form data');
    return { success: false, error: 'Agent ID and Organization ID are required.' };
  }

  try {
    const supabase = await createClient();

    // Duplicate the agent in Supabase
    const [duplicateError, newAgent] = await tryCatch(() =>
      supabaseQueries.duplicateAgent(supabase, agentId, newName)
    );

    if (duplicateError || !newAgent) {
      console.error('Error duplicating agent:', duplicateError);
      return {
        success: false,
        error: duplicateError?.message || 'Failed to duplicate agent'
      };
    }

    // If the duplicated agent is of type 'elevenlabs', create a new ElevenLabs agent
    if (newAgent.type === 'elevenlabs' && newAgent.config) {
      try {
        // Get the config from the agent
        const conversationConfig = (newAgent.config as any) || {};

        // Create a new ElevenLabs agent with the same configuration
        const formattedName = `${newAgent.name || `Agent ${newAgent.id}`} [SB-${orgId}]`;
        const [elevenlabsError, elevenLabsAgent] = await tryCatch(() =>
          elevenlabsQueries.createAgent(formattedName, conversationConfig)
        );

        if (elevenlabsError) {
          console.error('Error creating ElevenLabs agent:', elevenlabsError);
          // Continue even if ElevenLabs creation fails
        } else if (elevenLabsAgent) {
          // Update the agent with the new ElevenLabs agent ID
          await supabase
            .from('agents')
            .update({ external_id: elevenLabsAgent.agent_id })
            .eq('id', newAgent.id);
        }
      } catch (error) {
        console.error('Error in ElevenLabs agent creation:', error);
        // Continue even if ElevenLabs creation fails
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/${orgId}/agents`);

    return { success: true, id: newAgent.id };
  } catch (error) {
    console.error('Error in duplicateAgent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
