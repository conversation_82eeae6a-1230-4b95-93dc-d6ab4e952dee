'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { VoiceSelectionDialog } from '../VoiceSelectionDialog'
import { LanguageSelector } from '@/components/agents/language-selector'

// Types for ElevenLabs voices
interface ElevenLabsVoice {
  voice_id: string
  name: string
  category?: string
  description?: string
  preview_url?: string
  labels?: Record<string, string>
}

interface VoiceTabProps {
  setIsDirty: (dirty: boolean) => void
  initialValues?: {
    voice?: string
    voiceLanguage?: string
    latencyOptimization?: number
    stability?: number
    speed?: number
    similarity?: number
  }
}



export function AgentVoiceTab({ setIsDirty, initialValues = {} }: VoiceTabProps) {
  // Form data state
  const [formData, setFormData] = useState({
    voice: initialValues.voice || '',
    voiceLanguage: initialValues.voiceLanguage || 'en',
    latencyOptimization: initialValues.latencyOptimization || 0.5,
    stability: initialValues.stability || 0.7,
    speed: initialValues.speed || 1.0,
    similarity: initialValues.similarity || 0.75
  })

  // Get the selected voice name for display
  const { data: voices, isLoading } = useQuery({
    queryKey: ['elevenlabs-voices'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/elevenlabs/voices')
        if (!response.ok) {
          throw new Error('Failed to fetch voices')
        }
        const data = await response.json()
        return data.voices || []
      } catch (error) {
        console.error('Error fetching ElevenLabs voices:', error)
        return []
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2
  })

  // Filter voices by selected language
  const [filteredVoices, setFilteredVoices] = useState<ElevenLabsVoice[]>([])

  useEffect(() => {
    if (voices && voices.length > 0) {
      // Filter voices by language
      const languageCode = formData.voiceLanguage.toLowerCase()
      const filtered = voices.filter((voice: ElevenLabsVoice) => {
        // Check if voice has language label that matches selected language
        if (voice.labels?.language) {
          return voice.labels.language.toLowerCase() === languageCode
        }
        return false
      })

      setFilteredVoices(filtered)

      // If current selected voice is not in filtered list, reset it
      if (formData.voice && filtered.length > 0) {
        const voiceExists = filtered.some((v: ElevenLabsVoice) => v.voice_id === formData.voice)
        if (!voiceExists) {
          updateForm({ voice: '' })
        }
      }
    }
  }, [voices, formData.voiceLanguage])

  // Find the selected voice name
  const selectedVoice = voices?.find((voice: ElevenLabsVoice) => voice.voice_id === formData.voice)

  // Get available languages from voices
  const languageSet = new Set<string>()
  if (voices) {
    voices.forEach((voice: ElevenLabsVoice) => {
      if (voice.labels?.language) {
        languageSet.add(voice.labels.language.toLowerCase())
      }
    })
  }

  // Update form data and set dirty state
  const updateForm = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }

  return (
    <div className="max-w-3xl space-y-8">
      {/* Hidden form fields for voice settings */}
      <input type="hidden" name="voice" value={formData.voice} />
      <input type="hidden" name="voiceLanguage" value={formData.voiceLanguage} />
      <input type="hidden" name="latencyOptimization" value={formData.latencyOptimization} />
      <input type="hidden" name="stability" value={formData.stability} />
      <input type="hidden" name="speed" value={formData.speed} />
      <input type="hidden" name="similarity" value={formData.similarity} />


          <div className="space-y-6">
            <div className="space-y-2">
                <Label>Voice Language</Label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Select the language for the voice. This will filter available voices.
              </p>
              <div className="mt-2">
                <LanguageSelector
                  value={formData.voiceLanguage}
                  onValueChange={(value) => updateForm({ voiceLanguage: value, voice: '' })}
                  placeholder="Select voice language"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Voice</Label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Select the ElevenLabs voice you want to use for the agent.
              </p>

              {isLoading ? (
                <div className="flex items-center justify-center h-10 mt-2 border dark:border-zinc-700 rounded-md">
                  <span className="text-sm text-muted-foreground">Loading voices...</span>
                </div>
              ) : filteredVoices.length === 0 ? (
                <div className="flex items-center justify-center h-10 mt-2 border dark:border-zinc-700 rounded-md">
                  <span className="text-sm text-muted-foreground">No voices available for this language</span>
                </div>
              ) : (
                <VoiceSelectionDialog
                  selectedVoiceId={formData.voice}
                  onVoiceSelect={(voiceId) => updateForm({ voice: voiceId })}
                  filteredVoices={filteredVoices}
                  triggerComponent={
                    <Button
                      variant="outline"
                      className="w-full mt-2 justify-between font-normal text-left"
                    >
                      {selectedVoice ? (
                        <span>
                          {selectedVoice.name}
                          {selectedVoice.labels?.accent ? ` (${selectedVoice.labels.accent})` : ''}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">Select a voice</span>
                      )}
                    </Button>
                  }
                />
              )}
            </div>

            <div className="space-y-2">
              <Label>Optimize streaming latency</Label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Configure latency optimizations for the speech generation. Latency can be optimized at the cost of quality.
              </p>
              <div className="flex items-center gap-4 pt-2">
                <Slider
                  value={[formData.latencyOptimization]}
                  onValueChange={([v]) => updateForm({ latencyOptimization: v })}
                  min={0}
                  max={1}
                  step={0.01}
                  className="flex-1"
                />
                <span className="w-12 text-right">{formData.latencyOptimization.toFixed(2)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Stability</Label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Higher values will make speech more consistent, but it can also make it sound monotone. Lower values will make speech sound more expressive, but may lead to instabilities.
              </p>
              <div className="flex items-center gap-4 pt-2">
                <Slider
                  value={[formData.stability]}
                  onValueChange={([v]) => updateForm({ stability: v })}
                  min={0}
                  max={1}
                  step={0.01}
                  className="flex-1"
                />
                <span className="w-12 text-right">{formData.stability.toFixed(2)}</span>
              </div>
            </div>


            <div className="space-y-2">
              <Label>Similarity</Label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Higher values will boost the overall clarity and consistency of the voice. Very high values may lead to artifacts. Adjusting this value to find the right balance is recommended.
              </p>
              <div className="flex items-center gap-4 pt-2">
                <Slider
                  value={[formData.similarity]}
                  onValueChange={([v]) => updateForm({ similarity: v })}
                  min={0}
                  max={1}
                  step={0.01}
                  className="flex-1"
                />
                <span className="w-12 text-right">{formData.similarity.toFixed(2)}</span>
              </div>
            </div>
          </div>

    </div>
  )
}
