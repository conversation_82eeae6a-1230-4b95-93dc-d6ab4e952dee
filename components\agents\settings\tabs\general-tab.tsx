'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { LanguageSelector, MultiLanguageSelector } from '@/components/agents/language-selector';
import { LLM_PROVIDERS, formatCost, getLLMProvidersByCategory } from '@/constants/llm-providers';

interface AgentGeneralTabProps {
  setIsDirty: (dirty: boolean) => void;
  initialValues?: {
    name?: string;
    defaultLanguage?: string;
    additionalLanguages?: string[];
    firstMessage?: string;
    systemPrompt?: string;
    llmProvider?: string;
    customLLM?: {
      serverUrl?: string;
      modelId?: string;
      apiKey?: string;
    };
    temperature?: number;
    tokenLimit?: number;
    useRAG?: boolean;
    teamId?: string | null;
    budget_cents?: number | null;
  };
}

export function AgentGeneralTab({
  setIsDirty,
  initialValues = {}
}: AgentGeneralTabProps) {
  const [formData, setFormData] = useState({
    defaultLanguage: initialValues.defaultLanguage || 'en',
    additionalLanguages: initialValues.additionalLanguages || [],
    firstMessage: initialValues.firstMessage || '',
    systemPrompt: initialValues.systemPrompt || '',
    llmProvider: initialValues.llmProvider || 'gpt-4o-mini',
    customLLM: {
      serverUrl: initialValues.customLLM?.serverUrl || '',
      modelId: initialValues.customLLM?.modelId || '',
      apiKey: initialValues.customLLM?.apiKey || ''
    },
    temperature: initialValues.temperature || 0.7,
    tokenLimit: initialValues.tokenLimit || -1,
    useRAG: initialValues.useRAG || false,
    teamId: initialValues.teamId || 'none',
    budget_cents: initialValues.budget_cents || 0
  });

  // State for teams
  const [teams, setTeams] = useState<{id: string, name: string | null}[]>([]);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);

  // Fetch teams when component mounts
  useEffect(() => {
    const fetchTeams = async () => {
      setIsLoadingTeams(true);
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('agent_teams')
          .select('id, name')
          .order('name');

        if (error) {
          console.error('Error fetching teams:', error);
        } else {
          setTeams(data || []);
        }
      } catch (error) {
        console.error('Error fetching teams:', error);
      } finally {
        setIsLoadingTeams(false);
      }
    };

    fetchTeams();
  }, []);

  const updateForm = (updates: Partial<typeof formData>) => {
    setFormData((prev) => ({ ...prev, ...updates }));
    setIsDirty(true);
  };

  return (
    <div className="max-w-3xl space-y-8">
      {/* Hidden form fields for conversation settings */}
      <input type="hidden" name="language" value={formData.defaultLanguage} />
      <input type="hidden" name="additionalLanguages" value={JSON.stringify(formData.additionalLanguages)} />
      <input type="hidden" name="firstMessage" value={formData.firstMessage} />
      <input type="hidden" name="prompt" value={formData.systemPrompt} />
      <input type="hidden" name="llmProvider" value={formData.llmProvider} />
      <input type="hidden" name="temperature" value={formData.temperature} />
      <input type="hidden" name="tokenLimit" value={formData.tokenLimit} />

      <section className="space-y-4 gap-2">
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5">
          <h4 className="text-lg font-semibold dark:text-zinc-100">Agent Language</h4>
          <p className="text-sm text-muted-foreground">
            Choose the default language the agent will communicate in.
          </p>
          <div className="mt-4">
            <LanguageSelector
              value={formData.defaultLanguage}
              onValueChange={(value) => updateForm({ defaultLanguage: value })}
              placeholder="Select primary language"
            />
          </div>
        </div>

        {/* Additional Languages */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5">
          <h4 className="text-lg font-semibold dark:text-zinc-100">Additional Languages</h4>
          <p className="text-sm text-muted-foreground">
            Select additional languages the agent can communicate in.
          </p>
          <div className="mt-4">
            <MultiLanguageSelector
              values={formData.additionalLanguages}
              onValuesChange={(values) => updateForm({ additionalLanguages: values })}
              placeholder="Select additional languages"
              maxSelections={5}
            />
          </div>
        </div>

        {/* First Message */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5">

          <Label htmlFor="firstMessage" className="dark:text-zinc-100">First Message</Label>
          <Textarea
            id="firstMessage"
            value={formData.firstMessage}
            onChange={(e) => updateForm({ firstMessage: e.target.value })}
            placeholder="Enter the first message..."
            className="mt-2"
          />
        </div>
        {/* System Prompt */}

        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <Label htmlFor="systemPrompt" className="dark:text-zinc-100">System Prompt</Label>
            <Textarea
              id="systemPrompt"
              value={formData.systemPrompt}
              onChange={(e) => updateForm({ systemPrompt: e.target.value })}
              placeholder="Enter system prompt..."
              className="mt-2 min-h-[150px]"
            />
        </div>

        {/* LLM Selection */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <Label className="dark:text-zinc-100">LLM Provider</Label>
          <p className="text-sm text-muted-foreground">
            Choose the language model for your agent. Pricing shown is approximate cost per minute.
          </p>
          <Select
            value={formData.llmProvider}
            onValueChange={(value) => updateForm({ llmProvider: value })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              {/* Gemini Models */}
              <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">Gemini Models</div>
              {LLM_PROVIDERS.filter(p => p.category === 'gemini').map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div className="flex justify-between items-center w-full">
                    <span>{provider.name}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      ~${provider.costPerMinute.toFixed(4)}/min
                    </span>
                  </div>
                </SelectItem>
              ))}

              {/* Claude Models */}
              <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-t mt-1 pt-2">Claude Models</div>
              {LLM_PROVIDERS.filter(p => p.category === 'claude').map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div className="flex justify-between items-center w-full">
                    <span>{provider.name}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      ~${provider.costPerMinute.toFixed(4)}/min
                    </span>
                  </div>
                </SelectItem>
              ))}

              {/* GPT Models */}
              <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-t mt-1 pt-2">GPT Models</div>
              {LLM_PROVIDERS.filter(p => p.category === 'gpt').map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div className="flex justify-between items-center w-full">
                    <span>{provider.name}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      ~${provider.costPerMinute.toFixed(4)}/min
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Temperature */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <Label className="dark:text-zinc-100">Temperature</Label>
          <div className="flex items-center gap-4">
            <Slider
              value={[formData.temperature]}
              onValueChange={([value]) => updateForm({ temperature: value })}
              max={1}
              step={0.1}
              className="flex-1"
            />
            <span className="w-12 text-right dark:text-zinc-100">{formData.temperature}</span>
          </div>
        </div>

        {/* Token Limit */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <Label className="dark:text-zinc-100">Token Limit</Label>
          <Input
            type="number"
            value={formData.tokenLimit}
            onChange={(e) =>
              updateForm({ tokenLimit: parseInt(e.target.value) })
            }
          />
        </div>

        {/* Team Selection */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <h4 className="text-lg font-semibold dark:text-zinc-100">Team Assignment</h4>
          <p className="text-sm text-muted-foreground">
            Assign this agent to a team for better organization and budget tracking.
          </p>
          <div className="mt-4">
            <Select
              value={formData.teamId}
              onValueChange={(value) => updateForm({ teamId: value })}
              name="teamId"
            >
              <SelectTrigger className="w-full" disabled={isLoadingTeams}>
                <SelectValue placeholder={isLoadingTeams ? "Loading teams..." : "Select a team"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No team</SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name || 'Unnamed Team'}
                  </SelectItem>
                ))}
                {teams.length === 0 && !isLoadingTeams && (
                  <SelectItem value="no-teams-available" disabled>
                    No teams available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Budget */}
        <div className="rounded-xl bg-gray-50 dark:bg-zinc-800 border dark:border-zinc-700 p-6 flex-column gap-5 mt-6">
          <h4 className="text-lg font-semibold dark:text-zinc-100">Budget</h4>
          <p className="text-sm text-muted-foreground">
            Set the maximum budget for this agent in USD. This will be used to track spending.
          </p>
          <div className="relative mt-4">
            <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
            <Input
              type="number"
              min="0"
              step="100"
              value={(formData.budget_cents || 0) / 100}
              onChange={(e) =>
                updateForm({ budget_cents: Math.round(parseFloat(e.target.value) * 100) })
              }
              className="pl-7"
              name="budget"
            />
          </div>
        </div>

      </section>
    </div>
  );
}
