'use client'

import { useActionState, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { AgentGeneralTab } from './tabs/general-tab'
import { AgentVoiceTab } from './tabs/voice-tab'
import { AgentAnalysisTab } from './tabs/analysis-tab'
import { AgentSecurityTab } from './tabs/security-tab'
import { AgentAdvancedTab } from './tabs/advanced-tab'
import { AgentWidgetTab } from './tabs/widget-tab'
import { updateAgent } from '@/actions/serverActions'
import { getAgentDetails } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/client'
import { AgentConfig } from '@/types/database'

// Define props interface
interface AgentSettingsFormProps {
  agentId: string;
  orgId: string;
}

// Function to fetch agent data
async function fetchAgentData(agentId: string) {
  const supabase = createClient()
  const agentDetails = await getAgentDetails(supabase, agentId)

  return agentDetails
}

export function AgentSettingsForm(props: AgentSettingsFormProps) {
  const [isDirty, setIsDirty] = useState(false)
  // Extract IDs from props
  const { agentId, orgId } = props;

  // Fetch agent data
  const { data: agentData } = useQuery({
    queryKey: ['agent-details', agentId],
    queryFn: () => fetchAgentData(agentId),
    enabled: !!agentId
  })

  // Agent config is now part of the agent data
  const agentConfig = agentData?.config;

  // Helper function to safely access custom_metadata properties
  const getMetadataValue = (config: AgentConfig | null | undefined, key: string, defaultValue: any): any => {
    if (!config || !config.custom_metadata) return defaultValue;

    const metadata = config.custom_metadata as Record<string, any>;
    return metadata[key] !== undefined ? metadata[key] : defaultValue;
  };

  // Extract voice settings from agent config
  const voiceSettings = {
    voice: agentConfig?.voice_model || '',
    latencyOptimization: getMetadataValue(agentConfig, 'latencyOptimization', 0.5),
    stability: getMetadataValue(agentConfig, 'stability', 0.7),
    speed: getMetadataValue(agentConfig, 'speed', 1.0),
    similarity: getMetadataValue(agentConfig, 'similarity', 0.75)
  }

  const initialState = { success: false, error: '', id: '' };

  const [state, formAction, isPending] = useActionState(updateAgent, initialState)

  const handleSubmit = async (formData: FormData) => {
    formData.append('orgId', orgId)
    return formAction(formData)
  }


  return (
    <form action={handleSubmit} className="relative min-h-screen pb-16">
      {/* Hidden inputs to pass IDs to the server action */}
      <input type="hidden" name="agentId" value={agentId} />
      <input type="hidden" name="orgId" value={orgId} />

      <Tabs defaultValue="agent" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="agent">Agent</TabsTrigger>
          <TabsTrigger value="voice">Voice</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="agent" className='focus:ring-0'>
          {/* Pass agent data to tabs if needed for default values */}
          <AgentGeneralTab
            setIsDirty={setIsDirty}
            initialValues={{
              name: agentData?.name,
              teamId: agentData?.team_id,
              budget_cents: agentData?.budget_cents,
              // Add other general tab fields here
            }}
          />
        </TabsContent>

        <TabsContent value="voice" className='focus:ring-0'>
          <AgentVoiceTab
            setIsDirty={setIsDirty}
            initialValues={voiceSettings}
          />
        </TabsContent>




        <TabsContent value="advanced" className='focus:ring-0'>
          <AgentAdvancedTab setIsDirty={setIsDirty} />
        </TabsContent>


      </Tabs>

      {isDirty && (
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 flex items-center justify-between">
          <span>You have unsaved changes</span>
          <div className="space-x-2">
            <Button type="button" variant="outline" onClick={() => setIsDirty(false)}>
              Clear
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </div>
      )}
    </form>
  )
}
